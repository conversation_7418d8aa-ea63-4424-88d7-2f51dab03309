import 'package:flutter/material.dart';

import '../../../localization/repositories/strings.dart';
import '../models/item.dart';
import '../models/tip.dart';

abstract class ItemDefinitionState {
  Strings get strings;

  List<Item> get itemList;
  void onItemNameChanged(String itemId, String name);
  void onItemPriceChanged(String itemId, String price);
  void onItemQuantityChanged(String itemId, String quantity);
  void onItemTotalChanged(String itemId, String total);
  void addItem();
  void removeItem(String itemId);
  String? validateItemPrice(String? price);
  String? validateItemQuantity(String? quantity);
  String? validateItemTotal(String? total);

  Tip? get tip;
  void onTipPercentageChanged(String percentage);
  void onTipAmountChanged(String amount);
  void addTip();
  void removeTip();
  String? validateTipPercentage(String? percentage);
  String? validateTipAmount(String? amount);

  double get billTotal;
  String get billTotalFirstLine;
  String get billTotalSecondLine;

  GlobalKey<FormState> get formKey;
  AutovalidateMode get autovalidateMode;
  void continueToNextStep(BuildContext context);
}
