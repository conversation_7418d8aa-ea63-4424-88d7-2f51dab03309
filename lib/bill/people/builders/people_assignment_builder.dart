import 'package:flutter/material.dart';

import '../../../components/button/button.dart';
import '../../../locator/repositories/locator.dart';
import '../states/people_assignment_state.dart';
import '../views/assignment_total.dart';
import '../views/item_assignment_list.dart';
import '../views/people_assignment_page.dart';
import '../views/people_list.dart';

class PeopleAssignmentBuilder extends StatelessWidget {
  const PeopleAssignmentBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    final state = Locator.instance.get<PeopleAssignmentState>();
    return ListenableBuilder(
      listenable: state as ChangeNotifier,
      builder: (context, child) {
        return PeopleAssignmentPage(
          formKey: state.formKey,
          returnButton: Button(
            label: state.strings.returnLabel,
            onPressed: () => state.returnToPreviousStep(context),
          ),
          total: AssignmentTotal(
            firstLine: state.totalFirstLine,
            secondLine: state.totalSecondLine,
          ),
          continueButton: Button(
            label: state.strings.continueLabel,
            onPressed: state.canContinueToNextStep ? () => state.continueToNextStep(context) : null,
          ),
          itemList: ItemAssignmentList(
            itemList: state.itemList,
            onToggleExpansion: state.toggleItemExpansion,
            isItemExpanded: state.isItemExpanded,
            getAssignmentSummary: state.getItemAssignmentSummary,
            getUnassignedQuantityText: state.getItemUnassignedQuantityText,
            peopleListBuilder: (itemId) => PeopleList(
              itemId: itemId,
              peopleList: state.peopleList,
              onPersonNameChanged: state.onPersonNameChanged,
              onAddPerson: state.addPerson,
              onRemovePerson: state.removePerson,
              onToggleAssignment: (personId) => state.togglePersonAssignment(itemId, personId),
              onAddQuantity: (personId) => state.addPersonQuantity(itemId, personId),
              onSubtractQuantity: (personId) => state.subtractPersonQuantity(itemId, personId),
              isPersonAssigned: (personId) => state.isPersonAssignedToItem(itemId, personId),
              canPersonAddQuantity: (personId) => state.canPersonAddQuantity(itemId, personId),
              canPersonSubtractQuantity: (personId) => state.canPersonSubtractQuantity(itemId, personId),
              isPersonCheckboxEnabled: (personId) => state.isPersonCheckboxEnabled(itemId, personId),
              getPersonQuantity: (personId) => state.getPersonQuantityForItem(itemId, personId),
              isPersonEqualSplit: (personId) => state.isPersonEqualSplitForItem(itemId, personId),
              getPersonAmount: (personId) => state.getPersonAmountForItem(itemId, personId),
              onHideList: () => state.toggleItemExpansion(itemId),
              autoOpenNextItem: _shouldAutoOpenNextItem(state, itemId),
            ),
          ),
        );
      },
    );
  }

  bool _shouldAutoOpenNextItem(PeopleAssignmentState state, String currentItemId) {
    final currentIndex = state.itemList.indexWhere((item) => item.id == currentItemId);
    return currentIndex < state.itemList.length - 1;
  }
}
