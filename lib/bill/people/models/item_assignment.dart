import '../models/person.dart';

class ItemAssignment {
  const ItemAssignment({
    required this.itemId,
    required this.assignedPeople,
    this.isExpanded = false,
  });

  final String itemId;
  final List<Person> assignedPeople;
  final bool isExpanded;

  Map<String, dynamic> toJson() {
    return {
      'itemId': itemId,
      'assignedPeople': assignedPeople.map((person) => person.toJson()).toList(),
      'isExpanded': isExpanded,
    };
  }

  factory ItemAssignment.fromJson(Map<String, dynamic> json) {
    return ItemAssignment(
      itemId: json['itemId'],
      assignedPeople: (json['assignedPeople'] as List)
          .map((personJson) => Person.fromJson(personJson))
          .toList(),
      isExpanded: json['isExpanded'] ?? false,
    );
  }

  ItemAssignment copyWith({
    List<Person>? assignedPeople,
    bool? isExpanded,
  }) {
    return ItemAssignment(
      itemId: itemId,
      assignedPeople: assignedPeople ?? this.assignedPeople,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }

  /// Returns the total assigned quantity for this item
  int get totalAssignedQuantity {
    return assignedPeople
        .where((person) => person.isAssigned)
        .fold(0, (sum, person) => sum + (person.isEqualSplit ? 1 : person.quantity));
  }

  /// Returns the total assigned amount for this item
  double get totalAssignedAmount {
    return assignedPeople
        .where((person) => person.isAssigned)
        .fold(0.0, (sum, person) => sum + person.amount);
  }

  /// Returns the number of people assigned with equal split
  int get equalSplitPeopleCount {
    return assignedPeople
        .where((person) => person.isAssigned && person.isEqualSplit)
        .length;
  }

  /// Returns true if all people are using equal split
  bool get isAllEqualSplit {
    final assignedCount = assignedPeople.where((person) => person.isAssigned).length;
    return assignedCount > 0 && equalSplitPeopleCount == assignedCount;
  }

  /// Generates assignment summary text for display
  String generateAssignmentSummary() {
    final assigned = assignedPeople.where((person) => person.isAssigned).toList();
    
    if (assigned.isEmpty) return '';
    
    if (assigned.length == 1) {
      final person = assigned.first;
      final name = person.name.isEmpty ? person.number.toString() : person.name;
      return '$name: \$${person.amount.toStringAsFixed(2)}';
    }
    
    if (isAllEqualSplit) {
      final names = assigned.map((person) => 
          person.name.isEmpty ? person.number.toString() : person.name).join(' and ');
      return '$names: \$${assigned.first.amount.toStringAsFixed(2)} each';
    }
    
    // Mixed assignment
    final parts = assigned.map((person) {
      final name = person.name.isEmpty ? person.number.toString() : person.name;
      final amount = '\$${person.amount.toStringAsFixed(2)}';
      if (person.isEqualSplit) {
        return '$name: $amount';
      } else {
        return '$name: $amount (${person.quantity})';
      }
    }).toList();
    
    return parts.join(', ');
  }
}
