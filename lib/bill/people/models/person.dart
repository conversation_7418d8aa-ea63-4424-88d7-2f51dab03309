class Person {
  const Person({
    required this.id,
    this.number = 1,
    this.name = '',
    this.quantity = 0,
    this.amount = 0.0,
    this.isAssigned = false,
    this.isEqualSplit = false,
  });

  final String id;
  final int number;
  final String name;
  final int quantity;
  final double amount;
  final bool isAssigned;
  final bool isEqualSplit;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'number': number,
      'name': name,
      'quantity': quantity,
      'amount': amount,
      'isAssigned': isAssigned,
      'isEqualSplit': isEqualSplit,
    };
  }

  factory Person.fromJson(Map<String, dynamic> json) {
    return Person(
      id: json['id'],
      number: json['number'],
      name: json['name'],
      quantity: json['quantity'],
      amount: json['amount'],
      isAssigned: json['isAssigned'],
      isEqualSplit: json['isEqualSplit'],
    );
  }

  Person copyWith({
    String? name,
    int? number,
    int? quantity,
    double? amount,
    bool? isAssigned,
    bool? isEqualSplit,
  }) {
    return Person(
      id: id,
      number: number ?? this.number,
      name: name ?? this.name,
      quantity: quantity ?? this.quantity,
      amount: amount ?? this.amount,
      isAssigned: isAssigned ?? this.isAssigned,
      isEqualSplit: isEqualSplit ?? this.isEqualSplit,
    );
  }

  /// Returns the display text for quantity
  String get quantityDisplayText {
    if (isEqualSplit) {
      return 'Equal split';
    }
    return quantity.toString();
  }

  /// Returns true if this person can be deleted (when there are multiple people)
  bool canBeDeleted(int totalPeopleCount) {
    return totalPeopleCount > 1;
  }

  /// Returns true if add button should be enabled
  bool get canAddQuantity {
    return isAssigned;
  }

  /// Returns true if subtract button should be enabled
  bool get canSubtractQuantity {
    return isAssigned && (!isEqualSplit && quantity > 0);
  }
}
