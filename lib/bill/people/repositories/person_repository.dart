import '../models/person.dart';
import '../models/item_assignment.dart';

abstract class PersonRepository {
  Future<List<Person>> getPeopleList();
  Person generatePerson(int number);
  Future<List<Person>> createPerson([Person? person]);
  Future<List<Person>> update<PERSON>erson(Person person);
  Future<List<Person>> deletePerson(String personId);
  
  Future<List<ItemAssignment>> getItemAssignments();
  Future<List<ItemAssignment>> updateItemAssignment(ItemAssignment assignment);
  Future<List<ItemAssignment>> assignPersonToItem(String itemId, String personId);
  Future<List<ItemAssignment>> unassignPersonFromItem(String itemId, String personId);
  Future<List<ItemAssignment>> updatePersonQuantityForItem(String itemId, String personId, int quantity, bool isEqualSplit);
}
