import '../../../local_storage/models/local_storage_key.dart';
import '../../../local_storage/services/local_storage_service.dart';
import '../../../unique_identifier/services/unique_identifier_service.dart';
import '../models/person.dart';
import '../models/item_assignment.dart';
import 'person_repository.dart';

class PersonRepositoryImplementation implements PersonRepository {
  PersonRepositoryImplementation({
    required LocalStorageService localStorageService,
    required UniqueIdentifierService uniqueIdentifierService,
  }) : _localStorageService = localStorageService,
       _uniqueIdentifierService = uniqueIdentifierService;

  final LocalStorageService _localStorageService;
  final UniqueIdentifierService _uniqueIdentifierService;

  @override
  Future<List<Person>> getPeopleList() async {
    final peopleListJson = await _localStorageService.getValue<List<dynamic>>(
      LocalStorageKey.peopleList,
    );
    if (peopleListJson == null) return [];
    return peopleListJson.map((personJson) => Person.fromJson(personJson)).toList();
  }

  Future<void> _savePeopleList(List<Person> people) {
    return _localStorageService.setValue(
      LocalStorageKey.peopleList,
      people.map((person) => person.toJson()).toList(),
    );
  }

  @override
  Person generatePerson(int number) {
    final newPersonId = _uniqueIdentifierService.generate();
    return Person(id: newPersonId, number: number);
  }

  @override
  Future<List<Person>> createPerson([Person? person]) async {
    final peopleList = await getPeopleList();
    final nextNumber = peopleList.isEmpty ? 1 : peopleList.length + 1;
    final newPerson = person ?? generatePerson(nextNumber);
    peopleList.add(newPerson);
    await _savePeopleList(peopleList);
    return peopleList;
  }

  @override
  Future<List<Person>> updatePerson(Person person) async {
    final peopleList = await getPeopleList();
    final personIndex = peopleList.indexWhere((p) => p.id == person.id);
    if (personIndex == -1) {
      throw ArgumentError('Person with id "${person.id}" not found');
    }
    peopleList[personIndex] = person;
    await _savePeopleList(peopleList);
    return peopleList;
  }

  @override
  Future<List<Person>> deletePerson(String personId) async {
    final peopleList = await getPeopleList();
    
    // Check if person exists
    final personIndex = peopleList.indexWhere((p) => p.id == personId);
    if (personIndex == -1) {
      throw ArgumentError('Person with id "$personId" not found');
    }

    // Prevent deletion if this is the only person in the list
    if (peopleList.length == 1) {
      throw StateError('Cannot delete the only person in the list');
    }

    peopleList.removeAt(personIndex);
    
    // Renumber remaining people
    for (int i = 0; i < peopleList.length; i++) {
      peopleList[i] = peopleList[i].copyWith(number: i + 1);
    }
    
    await _savePeopleList(peopleList);
    return peopleList;
  }

  @override
  Future<List<ItemAssignment>> getItemAssignments() async {
    final assignmentsJson = await _localStorageService.getValue<List<dynamic>>(
      LocalStorageKey.itemAssignments,
    );
    if (assignmentsJson == null) return [];
    return assignmentsJson.map((assignmentJson) => ItemAssignment.fromJson(assignmentJson)).toList();
  }

  Future<void> _saveItemAssignments(List<ItemAssignment> assignments) {
    return _localStorageService.setValue(
      LocalStorageKey.itemAssignments,
      assignments.map((assignment) => assignment.toJson()).toList(),
    );
  }

  @override
  Future<List<ItemAssignment>> updateItemAssignment(ItemAssignment assignment) async {
    final assignments = await getItemAssignments();
    final assignmentIndex = assignments.indexWhere((a) => a.itemId == assignment.itemId);
    
    if (assignmentIndex == -1) {
      assignments.add(assignment);
    } else {
      assignments[assignmentIndex] = assignment;
    }
    
    await _saveItemAssignments(assignments);
    return assignments;
  }

  @override
  Future<List<ItemAssignment>> assignPersonToItem(String itemId, String personId) async {
    final assignments = await getItemAssignments();
    final assignmentIndex = assignments.indexWhere((a) => a.itemId == itemId);
    
    if (assignmentIndex == -1) {
      // Create new assignment
      final people = await getPeopleList();
      final person = people.firstWhere((p) => p.id == personId);
      final updatedPerson = person.copyWith(isAssigned: true);
      final newAssignment = ItemAssignment(
        itemId: itemId,
        assignedPeople: [updatedPerson],
      );
      assignments.add(newAssignment);
    } else {
      // Update existing assignment
      final assignment = assignments[assignmentIndex];
      final updatedPeople = assignment.assignedPeople.map((person) {
        if (person.id == personId) {
          return person.copyWith(isAssigned: true);
        }
        return person;
      }).toList();
      
      assignments[assignmentIndex] = assignment.copyWith(assignedPeople: updatedPeople);
    }
    
    await _saveItemAssignments(assignments);
    return assignments;
  }

  @override
  Future<List<ItemAssignment>> unassignPersonFromItem(String itemId, String personId) async {
    final assignments = await getItemAssignments();
    final assignmentIndex = assignments.indexWhere((a) => a.itemId == itemId);
    
    if (assignmentIndex != -1) {
      final assignment = assignments[assignmentIndex];
      final updatedPeople = assignment.assignedPeople.map((person) {
        if (person.id == personId) {
          return person.copyWith(isAssigned: false, quantity: 0, amount: 0.0);
        }
        return person;
      }).toList();
      
      assignments[assignmentIndex] = assignment.copyWith(assignedPeople: updatedPeople);
      await _saveItemAssignments(assignments);
    }
    
    return assignments;
  }

  @override
  Future<List<ItemAssignment>> updatePersonQuantityForItem(
    String itemId, 
    String personId, 
    int quantity, 
    bool isEqualSplit
  ) async {
    final assignments = await getItemAssignments();
    final assignmentIndex = assignments.indexWhere((a) => a.itemId == itemId);
    
    if (assignmentIndex != -1) {
      final assignment = assignments[assignmentIndex];
      final updatedPeople = assignment.assignedPeople.map((person) {
        if (person.id == personId) {
          return person.copyWith(
            quantity: quantity,
            isEqualSplit: isEqualSplit,
          );
        }
        return person;
      }).toList();
      
      assignments[assignmentIndex] = assignment.copyWith(assignedPeople: updatedPeople);
      await _saveItemAssignments(assignments);
    }
    
    return assignments;
  }
}
