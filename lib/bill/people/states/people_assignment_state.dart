import 'package:flutter/material.dart';

import '../../../localization/repositories/strings.dart';
import '../../items/models/item.dart';
import '../../items/models/tip.dart';
import '../models/person.dart';
import '../models/item_assignment.dart';

abstract class PeopleAssignmentState {
  Strings get strings;

  // Item management
  List<Item> get itemList;
  Tip? get tip;
  
  // People management
  List<Person> get peopleList;
  void onPersonNameChanged(String personId, String name);
  void addPerson();
  void removePerson(String personId);
  
  // Assignment management
  List<ItemAssignment> get itemAssignments;
  void toggleItemExpansion(String itemId);
  void togglePersonAssignment(String itemId, String personId);
  void updatePersonQuantity(String itemId, String personId, int quantity, bool isEqualSplit);
  void addPersonQuantity(String itemId, String personId);
  void subtractPersonQuantity(String itemId, String personId);
  
  // Item assignment display
  String getItemAssignmentSummary(String itemId);
  String getItemUnassignedQuantityText(String itemId);
  bool isItemExpanded(String itemId);
  bool isItemFullyAssigned(String itemId);
  
  // Person state for specific item
  bool isPersonAssignedToItem(String itemId, String personId);
  bool canPersonAddQuantity(String itemId, String personId);
  bool canPersonSubtractQuantity(String itemId, String personId);
  bool isPersonCheckboxEnabled(String itemId, String personId);
  int getPersonQuantityForItem(String itemId, String personId);
  bool isPersonEqualSplitForItem(String itemId, String personId);
  double getPersonAmountForItem(String itemId, String personId);
  
  // Total calculations
  double get totalAssignedAmount;
  double get totalTipAmount;
  double get grandTotal;
  String get totalFirstLine;
  String get totalSecondLine;
  
  // Navigation
  void returnToPreviousStep(BuildContext context);
  void continueToNextStep(BuildContext context);
  bool get canContinueToNextStep;
  
  // Form validation
  GlobalKey<FormState> get formKey;
  AutovalidateMode get autovalidateMode;
}
