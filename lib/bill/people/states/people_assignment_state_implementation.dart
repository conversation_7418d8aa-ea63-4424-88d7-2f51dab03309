import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../localization/repositories/locale_repository.dart';
import '../../../localization/repositories/strings.dart';
import '../../items/models/item.dart';
import '../../items/models/tip.dart';
import '../../items/repositories/item_repository.dart';
import '../../items/repositories/tip_repository.dart';
import '../models/person.dart';
import '../models/item_assignment.dart';
import '../repositories/person_repository.dart';
import 'people_assignment_state.dart';

class PeopleAssignmentStateImplementation extends ChangeNotifier
    implements PeopleAssignmentState {
  PeopleAssignmentStateImplementation({
    required PersonRepository personRepository,
    required ItemRepository itemRepository,
    required TipRepository tipRepository,
    required LocaleRepository localeRepository,
  }) : _personRepository = personRepository,
       _itemRepository = itemRepository,
       _tipRepository = tipRepository,
       _localeRepository = localeRepository {
    _loadData();
    _localeRepository.addListener(() => notifyListeners());
  }

  final PersonRepository _personRepository;
  final ItemRepository _itemRepository;
  final TipRepository _tipRepository;
  final LocaleRepository _localeRepository;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  AutovalidateMode _autovalidateMode = AutovalidateMode.disabled;

  @override
  Strings get strings => _localeRepository.strings;

  @override
  GlobalKey<FormState> get formKey => _formKey;

  @override
  AutovalidateMode get autovalidateMode => _autovalidateMode;

  List<Item> _itemList = [];
  @override
  List<Item> get itemList => _itemList;

  Tip? _tip;
  @override
  Tip? get tip => _tip;

  List<Person> _peopleList = [];
  @override
  List<Person> get peopleList => _peopleList;

  List<ItemAssignment> _itemAssignments = [];
  @override
  List<ItemAssignment> get itemAssignments => _itemAssignments;

  @override
  void dispose() {
    _localeRepository.removeListener(() => notifyListeners());
    super.dispose();
  }

  Future<void> _loadData() async {
    await Future.wait([
      _loadItemList(),
      _loadTip(),
      _loadPeopleList(),
      _loadItemAssignments(),
    ]);
    
    // Ensure first item is expanded and people exist
    _ensureInitialState();
    notifyListeners();
  }

  Future<void> _loadItemList() async {
    _itemList = await _itemRepository.getItemList();
  }

  Future<void> _loadTip() async {
    _tip = await _tipRepository.getTip();
  }

  Future<void> _loadPeopleList() async {
    _peopleList = await _personRepository.getPeopleList();
  }

  Future<void> _loadItemAssignments() async {
    _itemAssignments = await _personRepository.getItemAssignments();
  }

  void _ensureInitialState() {
    // Create first person if none exist
    if (_peopleList.isEmpty) {
      addPerson();
    }
    
    // Ensure first item is expanded
    if (_itemList.isNotEmpty) {
      final firstItemId = _itemList.first.id;
      final existingAssignment = _itemAssignments.firstWhere(
        (assignment) => assignment.itemId == firstItemId,
        orElse: () => ItemAssignment(itemId: firstItemId, assignedPeople: []),
      );
      
      if (!existingAssignment.isExpanded) {
        final updatedAssignment = existingAssignment.copyWith(isExpanded: true);
        _updateItemAssignment(updatedAssignment);
      }
    }
  }

  void _updateItemAssignment(ItemAssignment assignment) {
    final index = _itemAssignments.indexWhere((a) => a.itemId == assignment.itemId);
    if (index == -1) {
      _itemAssignments.add(assignment);
    } else {
      _itemAssignments[index] = assignment;
    }
    _personRepository.updateItemAssignment(assignment).ignore();
  }

  (int index, Person person) _getIndexedPerson(String personId) {
    return _peopleList.indexed.firstWhere(
      (indexedPerson) => indexedPerson.$2.id == personId,
    );
  }

  ItemAssignment _getOrCreateItemAssignment(String itemId) {
    return _itemAssignments.firstWhere(
      (assignment) => assignment.itemId == itemId,
      orElse: () => ItemAssignment(
        itemId: itemId,
        assignedPeople: _peopleList.map((person) => person.copyWith(isAssigned: false)).toList(),
      ),
    );
  }

  @override
  void onPersonNameChanged(String personId, String name) {
    final indexedPerson = _getIndexedPerson(personId);
    final updatedPerson = indexedPerson.$2.copyWith(name: name);
    _peopleList[indexedPerson.$1] = updatedPerson;
    
    // Update person in all item assignments
    for (int i = 0; i < _itemAssignments.length; i++) {
      final assignment = _itemAssignments[i];
      final updatedPeople = assignment.assignedPeople.map((person) {
        if (person.id == personId) {
          return person.copyWith(name: name);
        }
        return person;
      }).toList();
      _itemAssignments[i] = assignment.copyWith(assignedPeople: updatedPeople);
    }
    
    notifyListeners();
    _personRepository.updatePerson(updatedPerson).ignore();
  }

  @override
  void addPerson() {
    final nextNumber = _peopleList.length + 1;
    final newPerson = _personRepository.generatePerson(nextNumber);
    _peopleList.add(newPerson);
    
    // Add person to all item assignments
    for (int i = 0; i < _itemAssignments.length; i++) {
      final assignment = _itemAssignments[i];
      final updatedPeople = [...assignment.assignedPeople, newPerson.copyWith(isAssigned: false)];
      _itemAssignments[i] = assignment.copyWith(assignedPeople: updatedPeople);
    }
    
    // Enable checkbox for first person if this is the second person
    if (_peopleList.length == 2) {
      // This will be handled by the UI logic
    }
    
    notifyListeners();
    _personRepository.createPerson(newPerson).ignore();
  }

  @override
  void removePerson(String personId) {
    if (_peopleList.length <= 1) return;
    
    final personToRemove = _peopleList.firstWhere((p) => p.id == personId);
    _peopleList.removeWhere((p) => p.id == personId);
    
    // Renumber remaining people
    for (int i = 0; i < _peopleList.length; i++) {
      _peopleList[i] = _peopleList[i].copyWith(number: i + 1);
    }
    
    // Remove person from all item assignments and redistribute amounts
    for (int i = 0; i < _itemAssignments.length; i++) {
      final assignment = _itemAssignments[i];
      final removedPerson = assignment.assignedPeople.firstWhere(
        (p) => p.id == personId,
        orElse: () => personToRemove,
      );
      
      final updatedPeople = assignment.assignedPeople.where((p) => p.id != personId).toList();
      
      // Redistribute equal split amounts if the removed person was assigned
      if (removedPerson.isAssigned && removedPerson.isEqualSplit) {
        _redistributeEqualSplitAmount(assignment.itemId, updatedPeople);
      }
      
      _itemAssignments[i] = assignment.copyWith(assignedPeople: updatedPeople);
    }
    
    notifyListeners();
    _personRepository.deletePerson(personId).ignore();
  }

  void _redistributeEqualSplitAmount(String itemId, List<Person> remainingPeople) {
    final item = _itemList.firstWhere((item) => item.id == itemId);
    final assignedEqualSplitPeople = remainingPeople.where((p) => p.isAssigned && p.isEqualSplit).toList();
    
    if (assignedEqualSplitPeople.isNotEmpty) {
      final amountPerPerson = item.total / assignedEqualSplitPeople.length;
      for (final person in assignedEqualSplitPeople) {
        final index = remainingPeople.indexWhere((p) => p.id == person.id);
        remainingPeople[index] = person.copyWith(amount: amountPerPerson);
      }
    }
  }

  @override
  void toggleItemExpansion(String itemId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final wasExpanded = assignment.isExpanded;

    // Collapse all items first
    for (int i = 0; i < _itemAssignments.length; i++) {
      _itemAssignments[i] = _itemAssignments[i].copyWith(isExpanded: false);
    }

    // Expand this item if it wasn't expanded
    if (!wasExpanded) {
      final updatedAssignment = assignment.copyWith(isExpanded: true);
      _updateItemAssignment(updatedAssignment);
    }

    notifyListeners();
  }

  @override
  void togglePersonAssignment(String itemId, String personId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final person = assignment.assignedPeople.firstWhere((p) => p.id == personId);

    if (person.isAssigned) {
      // Unassign person
      final updatedPerson = person.copyWith(
        isAssigned: false,
        quantity: 0,
        amount: 0.0,
      );
      _updatePersonInAssignment(itemId, updatedPerson);
    } else {
      // Assign person
      final assignedPeople = assignment.assignedPeople.where((p) => p.isAssigned).toList();
      final hasEqualSplitPeople = assignedPeople.any((p) => p.isEqualSplit);

      Person updatedPerson;
      if (assignedPeople.isEmpty || hasEqualSplitPeople) {
        // Use equal split
        updatedPerson = person.copyWith(
          isAssigned: true,
          isEqualSplit: true,
          quantity: 1,
        );
      } else {
        // Use quantity-based assignment
        final unassignedQuantity = _getUnassignedQuantity(itemId);
        if (unassignedQuantity > 0) {
          updatedPerson = person.copyWith(
            isAssigned: true,
            isEqualSplit: false,
            quantity: 1,
          );
        } else {
          return; // Cannot assign if no quantity left
        }
      }

      _updatePersonInAssignment(itemId, updatedPerson);
      _recalculateAmounts(itemId);
    }

    notifyListeners();
  }

  void _updatePersonInAssignment(String itemId, Person updatedPerson) {
    final assignmentIndex = _itemAssignments.indexWhere((a) => a.itemId == itemId);
    if (assignmentIndex != -1) {
      final assignment = _itemAssignments[assignmentIndex];
      final updatedPeople = assignment.assignedPeople.map((person) {
        if (person.id == updatedPerson.id) {
          return updatedPerson;
        }
        return person;
      }).toList();

      _itemAssignments[assignmentIndex] = assignment.copyWith(assignedPeople: updatedPeople);
      _personRepository.updateItemAssignment(_itemAssignments[assignmentIndex]).ignore();
    }
  }

  void _recalculateAmounts(String itemId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final item = _itemList.firstWhere((item) => item.id == itemId);
    final assignedPeople = assignment.assignedPeople.where((p) => p.isAssigned).toList();

    if (assignedPeople.isEmpty) return;

    final equalSplitPeople = assignedPeople.where((p) => p.isEqualSplit).toList();
    final quantityPeople = assignedPeople.where((p) => !p.isEqualSplit).toList();

    // Calculate amounts for quantity-based people
    for (final person in quantityPeople) {
      final amount = item.price * person.quantity;
      final updatedPerson = person.copyWith(amount: amount);
      _updatePersonInAssignment(itemId, updatedPerson);
    }

    // Calculate amounts for equal split people
    if (equalSplitPeople.isNotEmpty) {
      final amountPerPerson = item.total / equalSplitPeople.length;
      for (final person in equalSplitPeople) {
        final updatedPerson = person.copyWith(amount: amountPerPerson);
        _updatePersonInAssignment(itemId, updatedPerson);
      }
    }
  }

  @override
  void updatePersonQuantity(String itemId, String personId, int quantity, bool isEqualSplit) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final person = assignment.assignedPeople.firstWhere((p) => p.id == personId);

    final updatedPerson = person.copyWith(
      quantity: quantity,
      isEqualSplit: isEqualSplit,
    );

    _updatePersonInAssignment(itemId, updatedPerson);
    _recalculateAmounts(itemId);
    notifyListeners();
  }

  @override
  void addPersonQuantity(String itemId, String personId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final person = assignment.assignedPeople.firstWhere((p) => p.id == personId);

    if (person.isEqualSplit) {
      // Convert to quantity-based
      updatePersonQuantity(itemId, personId, 1, false);
    } else {
      // Increase quantity
      final newQuantity = person.quantity + 1;
      updatePersonQuantity(itemId, personId, newQuantity, false);
    }
  }

  @override
  void subtractPersonQuantity(String itemId, String personId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final person = assignment.assignedPeople.firstWhere((p) => p.id == personId);

    if (!person.isEqualSplit && person.quantity > 1) {
      // Decrease quantity
      final newQuantity = person.quantity - 1;
      updatePersonQuantity(itemId, personId, newQuantity, false);
    } else if (!person.isEqualSplit && person.quantity == 1) {
      // Convert to equal split
      updatePersonQuantity(itemId, personId, 1, true);
    }
  }

  int _getUnassignedQuantity(String itemId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final item = _itemList.firstWhere((item) => item.id == itemId);
    final assignedQuantity = assignment.assignedPeople
        .where((p) => p.isAssigned && !p.isEqualSplit)
        .fold(0, (sum, person) => sum + person.quantity);
    return item.quantity - assignedQuantity;
  }

  // Display methods
  @override
  String getItemAssignmentSummary(String itemId) {
    final assignment = _itemAssignments.firstWhere(
      (a) => a.itemId == itemId,
      orElse: () => ItemAssignment(itemId: itemId, assignedPeople: []),
    );
    return assignment.generateAssignmentSummary();
  }

  @override
  String getItemUnassignedQuantityText(String itemId) {
    final unassignedQuantity = _getUnassignedQuantity(itemId);
    if (unassignedQuantity > 0) {
      return 'Unassigned quantity: $unassignedQuantity';
    }
    return '';
  }

  @override
  bool isItemExpanded(String itemId) {
    final assignment = _itemAssignments.firstWhere(
      (a) => a.itemId == itemId,
      orElse: () => ItemAssignment(itemId: itemId, assignedPeople: []),
    );
    return assignment.isExpanded;
  }

  @override
  bool isItemFullyAssigned(String itemId) {
    final item = _itemList.firstWhere((item) => item.id == itemId);
    final assignment = _getOrCreateItemAssignment(itemId);

    final assignedPeople = assignment.assignedPeople.where((p) => p.isAssigned).toList();
    if (assignedPeople.isEmpty) return false;

    // Check if using equal split
    if (assignedPeople.any((p) => p.isEqualSplit)) {
      return true; // Equal split is always considered fully assigned
    }

    // Check if all quantity is assigned
    final totalAssignedQuantity = assignedPeople.fold(0, (sum, person) => sum + person.quantity);
    return totalAssignedQuantity == item.quantity;
  }

  // Person state methods
  @override
  bool isPersonAssignedToItem(String itemId, String personId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final person = assignment.assignedPeople.firstWhere(
      (p) => p.id == personId,
      orElse: () => Person(id: personId),
    );
    return person.isAssigned;
  }

  @override
  bool canPersonAddQuantity(String itemId, String personId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final person = assignment.assignedPeople.firstWhere(
      (p) => p.id == personId,
      orElse: () => Person(id: personId),
    );
    return person.isAssigned && _peopleList.length > 1;
  }

  @override
  bool canPersonSubtractQuantity(String itemId, String personId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final person = assignment.assignedPeople.firstWhere(
      (p) => p.id == personId,
      orElse: () => Person(id: personId),
    );
    return person.isAssigned && (!person.isEqualSplit && person.quantity > 0);
  }

  @override
  bool isPersonCheckboxEnabled(String itemId, String personId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final person = assignment.assignedPeople.firstWhere(
      (p) => p.id == personId,
      orElse: () => Person(id: personId),
    );

    if (person.isAssigned) return true;

    // Check if there's unassigned quantity or if using equal split
    final assignedPeople = assignment.assignedPeople.where((p) => p.isAssigned).toList();
    if (assignedPeople.any((p) => p.isEqualSplit)) return true;

    return _getUnassignedQuantity(itemId) > 0;
  }

  @override
  int getPersonQuantityForItem(String itemId, String personId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final person = assignment.assignedPeople.firstWhere(
      (p) => p.id == personId,
      orElse: () => Person(id: personId),
    );
    return person.quantity;
  }

  @override
  bool isPersonEqualSplitForItem(String itemId, String personId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final person = assignment.assignedPeople.firstWhere(
      (p) => p.id == personId,
      orElse: () => Person(id: personId),
    );
    return person.isEqualSplit;
  }

  @override
  double getPersonAmountForItem(String itemId, String personId) {
    final assignment = _getOrCreateItemAssignment(itemId);
    final person = assignment.assignedPeople.firstWhere(
      (p) => p.id == personId,
      orElse: () => Person(id: personId),
    );
    return person.amount;
  }

  // Total calculations
  @override
  double get totalAssignedAmount {
    return _itemAssignments.fold(0.0, (total, assignment) {
      return total + assignment.totalAssignedAmount;
    });
  }

  @override
  double get totalTipAmount {
    if (_tip == null) return 0.0;
    return totalAssignedAmount * (_tip!.percentage / 100);
  }

  @override
  double get grandTotal {
    return totalAssignedAmount + totalTipAmount;
  }

  @override
  String get totalFirstLine {
    if (_tip == null || _tip!.percentage == 0) {
      return '\$${totalAssignedAmount.toStringAsFixed(2)}';
    }
    return '\$${totalAssignedAmount.toStringAsFixed(2)} + \$${totalTipAmount.toStringAsFixed(2)}';
  }

  @override
  String get totalSecondLine {
    return '\$${grandTotal.toStringAsFixed(2)}';
  }

  // Navigation
  @override
  void returnToPreviousStep(BuildContext context) {
    // Collapse all items
    for (int i = 0; i < _itemAssignments.length; i++) {
      _itemAssignments[i] = _itemAssignments[i].copyWith(isExpanded: false);
    }
    notifyListeners();

    // Navigate to previous step
    context.go('/');
  }

  @override
  bool get canContinueToNextStep {
    // Check if all items are fully assigned
    for (final item in _itemList) {
      if (!isItemFullyAssigned(item.id)) {
        return false;
      }
    }
    return true;
  }

  @override
  void continueToNextStep(BuildContext context) {
    _autovalidateMode = AutovalidateMode.always;

    if (canContinueToNextStep) {
      // TODO: Navigate to next step (e.g., summary or payment page)
      // For now, this is the last step, so we could show a completion message
      // context.go('/summary');
    }

    notifyListeners();
  }
}
