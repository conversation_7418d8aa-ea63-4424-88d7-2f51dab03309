import 'package:flutter/material.dart';

import '../../items/models/item.dart';

class ItemAssignmentCard extends StatelessWidget {
  const ItemAssignmentCard({
    super.key,
    required this.item,
    required this.isExpanded,
    required this.assignmentSummary,
    required this.unassignedQuantityText,
    required this.onToggleExpansion,
    this.peopleList,
  });

  final Item item;
  final bool isExpanded;
  final String assignmentSummary;
  final String unassignedQuantityText;
  final VoidCallback onToggleExpansion;
  final Widget? peopleList;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Item header
            Row(
              children: [
                // Order number
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      '${item.quantity}', // Using quantity as order for now
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Item details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.name.isEmpty ? 'Item ${item.quantity}' : item.name,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '\$${item.price.toStringAsFixed(2)} x ${item.quantity} = \$${item.total.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Assign people button
                ElevatedButton(
                  onPressed: onToggleExpansion,
                  child: Text(isExpanded ? 'Hide People' : 'Assign People'),
                ),
              ],
            ),
            
            // Assignment summary (when collapsed and has assignments)
            if (!isExpanded && assignmentSummary.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                assignmentSummary,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
            
            // Unassigned quantity (when collapsed and has unassigned)
            if (!isExpanded && unassignedQuantityText.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                unassignedQuantityText,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.orange[700],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
            
            // People list (when expanded)
            if (isExpanded && peopleList != null) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              peopleList!,
            ],
          ],
        ),
      ),
    );
  }
}
