import 'package:flutter/material.dart';

import '../../items/models/item.dart';
import 'item_assignment_card.dart';

class ItemAssignmentList extends StatelessWidget {
  const ItemAssignmentList({
    super.key,
    required this.itemList,
    required this.onToggleExpansion,
    required this.isItemExpanded,
    required this.getAssignmentSummary,
    required this.getUnassignedQuantityText,
    required this.peopleListBuilder,
  });

  final List<Item> itemList;
  final void Function(String itemId) onToggleExpansion;
  final bool Function(String itemId) isItemExpanded;
  final String Function(String itemId) getAssignmentSummary;
  final String Function(String itemId) getUnassignedQuantityText;
  final Widget Function(String itemId) peopleListBuilder;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: itemList.map((item) {
        final isExpanded = isItemExpanded(item.id);
        final assignmentSummary = getAssignmentSummary(item.id);
        final unassignedQuantityText = getUnassignedQuantityText(item.id);
        
        return ItemAssignmentCard(
          item: item,
          isExpanded: isExpanded,
          assignmentSummary: assignmentSummary,
          unassignedQuantityText: unassignedQuantityText,
          onToggleExpansion: () => onToggleExpansion(item.id),
          peopleList: isExpanded ? peopleListBuilder(item.id) : null,
        );
      }).toList(),
    );
  }
}
