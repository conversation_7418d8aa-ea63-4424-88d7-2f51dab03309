import 'package:flutter/material.dart';

class PeopleAssignmentPage extends StatelessWidget {
  const PeopleAssignmentPage({
    super.key,
    required this.itemList,
    required this.total,
    required this.returnButton,
    required this.continueButton,
    required this.formKey,
  });

  final Widget itemList;
  final Widget total;
  final Widget returnButton;
  final Widget continueButton;
  final GlobalKey<FormState> formKey;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BottomAppBar(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [returnButton, total, continueButton],
        ),
      ),
      body: Safe<PERSON>rea(
        child: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              children: [
                itemList,
                const SizedBox(height: 30),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
