import 'package:flutter/material.dart';

import '../models/person.dart';
import 'person_item.dart';

class PeopleList extends StatelessWidget {
  const PeopleList({
    super.key,
    required this.itemId,
    required this.peopleList,
    required this.onPersonNameChanged,
    required this.onAddPerson,
    required this.onRemovePerson,
    required this.onToggleAssignment,
    required this.onAddQuantity,
    required this.onSubtractQuantity,
    required this.isPersonAssigned,
    required this.canPersonAddQuantity,
    required this.canPersonSubtractQuantity,
    required this.isPersonCheckboxEnabled,
    required this.getPersonQuantity,
    required this.isPersonEqualSplit,
    required this.getPersonAmount,
    required this.onHideList,
    this.autoOpenNextItem = false,
  });

  final String itemId;
  final List<Person> peopleList;
  final void Function(String personId, String name) onPersonNameChanged;
  final VoidCallback onAddPerson;
  final void Function(String personId) onRemovePerson;
  final void Function(String personId) onToggleAssignment;
  final void Function(String personId) onAddQuantity;
  final void Function(String personId) onSubtractQuantity;
  final bool Function(String personId) isPersonAssigned;
  final bool Function(String personId) canPersonAddQuantity;
  final bool Function(String personId) canPersonSubtractQuantity;
  final bool Function(String personId) isPersonCheckboxEnabled;
  final int Function(String personId) getPersonQuantity;
  final bool Function(String personId) isPersonEqualSplit;
  final double Function(String personId) getPersonAmount;
  final VoidCallback onHideList;
  final bool autoOpenNextItem;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // People list
        ...peopleList.map((person) {
          return PersonItem(
            person: person,
            isAssigned: isPersonAssigned(person.id),
            canAddQuantity: canPersonAddQuantity(person.id),
            canSubtractQuantity: canPersonSubtractQuantity(person.id),
            isCheckboxEnabled: isPersonCheckboxEnabled(person.id),
            quantity: getPersonQuantity(person.id),
            isEqualSplit: isPersonEqualSplit(person.id),
            amount: getPersonAmount(person.id),
            canBeDeleted: person.canBeDeleted(peopleList.length),
            onNameChanged: (name) => onPersonNameChanged(person.id, name),
            onToggleAssignment: () => onToggleAssignment(person.id),
            onAddQuantity: () => onAddQuantity(person.id),
            onSubtractQuantity: () => onSubtractQuantity(person.id),
            onDelete: () => onRemovePerson(person.id),
          );
        }),
        
        const SizedBox(height: 16),
        
        // Action buttons
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: onAddPerson,
              icon: const Icon(Icons.add),
              label: const Text('Add Person'),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: onHideList,
              child: Text(autoOpenNextItem ? 'Next Item' : 'Hide List'),
            ),
          ],
        ),
      ],
    );
  }
}
