import 'package:flutter/material.dart';

import '../models/person.dart';

class PersonItem extends StatelessWidget {
  const PersonItem({
    super.key,
    required this.person,
    required this.isAssigned,
    required this.canAddQuantity,
    required this.canSubtractQuantity,
    required this.isCheckboxEnabled,
    required this.quantity,
    required this.isEqualSplit,
    required this.amount,
    required this.canBeDeleted,
    required this.onNameChanged,
    required this.onToggleAssignment,
    required this.onAddQuantity,
    required this.onSubtractQuantity,
    required this.onDelete,
  });

  final Person person;
  final bool isAssigned;
  final bool canAddQuantity;
  final bool canSubtractQuantity;
  final bool isCheckboxEnabled;
  final int quantity;
  final bool isEqualSplit;
  final double amount;
  final bool canBeDeleted;
  final void Function(String name) onNameChanged;
  final VoidCallback onToggleAssignment;
  final VoidCallback onAddQuantity;
  final VoidCallback onSubtractQuantity;
  final VoidCallback onDelete;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Person number
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  '${person.number}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Name field
            Expanded(
              flex: 2,
              child: TextFormField(
                initialValue: person.name,
                decoration: const InputDecoration(
                  labelText: 'Name',
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
                onChanged: onNameChanged,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Quantity controls
            Expanded(
              flex: 2,
              child: Row(
                children: [
                  // Subtract button
                  IconButton(
                    onPressed: canSubtractQuantity ? onSubtractQuantity : null,
                    icon: const Icon(Icons.remove),
                    iconSize: 20,
                  ),
                  
                  // Quantity display
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        isEqualSplit ? 'Equal split' : quantity.toString(),
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ),
                  
                  // Add button
                  IconButton(
                    onPressed: canAddQuantity ? onAddQuantity : null,
                    icon: const Icon(Icons.add),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Amount display
            Expanded(
              child: Text(
                '\$${amount.toStringAsFixed(2)}',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Checkbox
            Checkbox(
              value: isAssigned,
              onChanged: isCheckboxEnabled ? (_) => onToggleAssignment() : null,
            ),
            
            // Delete button
            if (canBeDeleted) ...[
              const SizedBox(width: 8),
              IconButton(
                onPressed: onDelete,
                icon: const Icon(Icons.delete),
                iconSize: 20,
                color: Colors.red,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
