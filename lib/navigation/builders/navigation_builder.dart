import 'package:go_router/go_router.dart';

import '../../bill/items/builders/item_definition_builder.dart';
import '../../bill/people/builders/people_assignment_builder.dart';

final router = GoRouter(
  routes: [
    GoRoute(path: '/', builder: (context, state) => const ItemDefinitionBuilder()),
    GoRoute(path: '/people-assignment', builder: (context, state) => const PeopleAssignmentBuilder()),
  ],
);
