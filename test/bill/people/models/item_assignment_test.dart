import 'package:bill_splitter_2/bill/people/models/item_assignment.dart';
import 'package:bill_splitter_2/bill/people/models/person.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ItemAssignment', () {
    const person1 = Person(
      id: 'person1',
      number: 1,
      name: 'Alice',
      quantity: 2,
      amount: 10.0,
      isAssigned: true,
      isEqualSplit: false,
    );

    const person2 = Person(
      id: 'person2',
      number: 2,
      name: '<PERSON>',
      quantity: 1,
      amount: 15.0,
      isAssigned: true,
      isEqualSplit: true,
    );

    const person3 = Person(
      id: 'person3',
      number: 3,
      name: '<PERSON>',
      quantity: 0,
      amount: 0.0,
      isAssigned: false,
      isEqualSplit: false,
    );

    group('constructor', () {
      test('creates item assignment with default values', () {
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [],
        );
        
        expect(assignment.itemId, 'item1');
        expect(assignment.assignedPeople, []);
        expect(assignment.isExpanded, false);
      });

      test('creates item assignment with custom values', () {
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person1, person2],
          isExpanded: true,
        );
        
        expect(assignment.itemId, 'item1');
        expect(assignment.assignedPeople, [person1, person2]);
        expect(assignment.isExpanded, true);
      });
    });

    group('toJson', () {
      test('converts item assignment to JSON correctly', () {
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person1],
          isExpanded: true,
        );
        
        final json = assignment.toJson();
        
        expect(json['itemId'], 'item1');
        expect(json['assignedPeople'], [person1.toJson()]);
        expect(json['isExpanded'], true);
      });
    });

    group('fromJson', () {
      test('creates item assignment from JSON correctly', () {
        final json = {
          'itemId': 'item1',
          'assignedPeople': [person1.toJson(), person2.toJson()],
          'isExpanded': false,
        };
        
        final assignment = ItemAssignment.fromJson(json);
        
        expect(assignment.itemId, 'item1');
        expect(assignment.assignedPeople.length, 2);
        expect(assignment.assignedPeople[0].id, person1.id);
        expect(assignment.assignedPeople[1].id, person2.id);
        expect(assignment.isExpanded, false);
      });

      test('handles missing isExpanded field', () {
        final json = {
          'itemId': 'item1',
          'assignedPeople': [],
        };
        
        final assignment = ItemAssignment.fromJson(json);
        
        expect(assignment.isExpanded, false);
      });
    });

    group('copyWith', () {
      test('creates copy with updated values', () {
        const original = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person1],
          isExpanded: false,
        );
        
        final updated = original.copyWith(
          assignedPeople: [person1, person2],
          isExpanded: true,
        );
        
        expect(updated.itemId, 'item1'); // unchanged
        expect(updated.assignedPeople, [person1, person2]); // changed
        expect(updated.isExpanded, true); // changed
      });
    });

    group('totalAssignedQuantity', () {
      test('calculates total assigned quantity correctly', () {
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person1, person2, person3],
        );
        
        // person1: assigned, quantity 2
        // person2: assigned, equal split (counts as 1)
        // person3: not assigned
        expect(assignment.totalAssignedQuantity, 3);
      });

      test('returns 0 when no people are assigned', () {
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person3],
        );
        
        expect(assignment.totalAssignedQuantity, 0);
      });
    });

    group('totalAssignedAmount', () {
      test('calculates total assigned amount correctly', () {
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person1, person2, person3],
        );
        
        // person1: $10.0, person2: $15.0, person3: not assigned
        expect(assignment.totalAssignedAmount, 25.0);
      });
    });

    group('equalSplitPeopleCount', () {
      test('counts equal split people correctly', () {
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person1, person2, person3],
        );
        
        // Only person2 is assigned with equal split
        expect(assignment.equalSplitPeopleCount, 1);
      });
    });

    group('isAllEqualSplit', () {
      test('returns true when all assigned people use equal split', () {
        const equalSplitPerson = Person(
          id: 'person4',
          isAssigned: true,
          isEqualSplit: true,
        );
        
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person2, equalSplitPerson, person3],
        );
        
        expect(assignment.isAllEqualSplit, true);
      });

      test('returns false when not all assigned people use equal split', () {
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person1, person2, person3],
        );
        
        expect(assignment.isAllEqualSplit, false);
      });

      test('returns false when no people are assigned', () {
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person3],
        );
        
        expect(assignment.isAllEqualSplit, false);
      });
    });

    group('generateAssignmentSummary', () {
      test('returns empty string when no people are assigned', () {
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person3],
        );
        
        expect(assignment.generateAssignmentSummary(), '');
      });

      test('returns single person summary', () {
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person1, person3],
        );
        
        expect(assignment.generateAssignmentSummary(), 'Alice: \$10.00');
      });

      test('returns equal split summary for multiple people', () {
        const equalSplitPerson1 = Person(
          id: 'person4',
          number: 4,
          name: 'David',
          amount: 12.50,
          isAssigned: true,
          isEqualSplit: true,
        );
        
        const equalSplitPerson2 = Person(
          id: 'person5',
          number: 5,
          name: '',
          amount: 12.50,
          isAssigned: true,
          isEqualSplit: true,
        );
        
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [equalSplitPerson1, equalSplitPerson2, person3],
        );
        
        expect(assignment.generateAssignmentSummary(), 'David and 5: \$12.50 each');
      });

      test('returns mixed assignment summary', () {
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [person1, person2, person3],
        );
        
        expect(assignment.generateAssignmentSummary(), 'Alice: \$10.00 (2), Bob: \$15.00');
      });

      test('uses person number when name is empty', () {
        const personWithoutName = Person(
          id: 'person4',
          number: 4,
          name: '',
          amount: 8.0,
          isAssigned: true,
          isEqualSplit: false,
          quantity: 1,
        );
        
        const assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [personWithoutName],
        );
        
        expect(assignment.generateAssignmentSummary(), '4: \$8.00');
      });
    });
  });
}
