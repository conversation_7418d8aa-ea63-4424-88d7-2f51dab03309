import 'package:bill_splitter_2/bill/people/models/person.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Person', () {
    group('constructor', () {
      test('creates person with default values', () {
        const person = Person(id: 'test-id');
        
        expect(person.id, 'test-id');
        expect(person.number, 1);
        expect(person.name, '');
        expect(person.quantity, 0);
        expect(person.amount, 0.0);
        expect(person.isAssigned, false);
        expect(person.isEqualSplit, false);
      });

      test('creates person with custom values', () {
        const person = Person(
          id: 'test-id',
          number: 3,
          name: '<PERSON>',
          quantity: 2,
          amount: 15.50,
          isAssigned: true,
          isEqualSplit: true,
        );
        
        expect(person.id, 'test-id');
        expect(person.number, 3);
        expect(person.name, 'Alice');
        expect(person.quantity, 2);
        expect(person.amount, 15.50);
        expect(person.isAssigned, true);
        expect(person.isEqualSplit, true);
      });
    });

    group('toJson', () {
      test('converts person to JSON correctly', () {
        const person = Person(
          id: 'test-id',
          number: 2,
          name: '<PERSON>',
          quantity: 3,
          amount: 25.75,
          isAssigned: true,
          isEqualSplit: false,
        );
        
        final json = person.toJson();
        
        expect(json, {
          'id': 'test-id',
          'number': 2,
          'name': 'Bob',
          'quantity': 3,
          'amount': 25.75,
          'isAssigned': true,
          'isEqualSplit': false,
        });
      });
    });

    group('fromJson', () {
      test('creates person from JSON correctly', () {
        final json = {
          'id': 'test-id',
          'number': 4,
          'name': 'Charlie',
          'quantity': 1,
          'amount': 12.25,
          'isAssigned': false,
          'isEqualSplit': true,
        };
        
        final person = Person.fromJson(json);
        
        expect(person.id, 'test-id');
        expect(person.number, 4);
        expect(person.name, 'Charlie');
        expect(person.quantity, 1);
        expect(person.amount, 12.25);
        expect(person.isAssigned, false);
        expect(person.isEqualSplit, true);
      });
    });

    group('copyWith', () {
      test('creates copy with updated values', () {
        const original = Person(
          id: 'test-id',
          number: 1,
          name: 'Original',
          quantity: 1,
          amount: 10.0,
          isAssigned: false,
          isEqualSplit: false,
        );
        
        final updated = original.copyWith(
          name: 'Updated',
          quantity: 2,
          amount: 20.0,
          isAssigned: true,
        );
        
        expect(updated.id, 'test-id'); // unchanged
        expect(updated.number, 1); // unchanged
        expect(updated.name, 'Updated'); // changed
        expect(updated.quantity, 2); // changed
        expect(updated.amount, 20.0); // changed
        expect(updated.isAssigned, true); // changed
        expect(updated.isEqualSplit, false); // unchanged
      });

      test('creates copy with no changes when no parameters provided', () {
        const original = Person(
          id: 'test-id',
          number: 2,
          name: 'Test',
          quantity: 3,
          amount: 15.0,
          isAssigned: true,
          isEqualSplit: true,
        );
        
        final copy = original.copyWith();
        
        expect(copy.id, original.id);
        expect(copy.number, original.number);
        expect(copy.name, original.name);
        expect(copy.quantity, original.quantity);
        expect(copy.amount, original.amount);
        expect(copy.isAssigned, original.isAssigned);
        expect(copy.isEqualSplit, original.isEqualSplit);
      });
    });

    group('quantityDisplayText', () {
      test('returns "Equal split" when isEqualSplit is true', () {
        const person = Person(
          id: 'test-id',
          quantity: 5,
          isEqualSplit: true,
        );
        
        expect(person.quantityDisplayText, 'Equal split');
      });

      test('returns quantity as string when isEqualSplit is false', () {
        const person = Person(
          id: 'test-id',
          quantity: 3,
          isEqualSplit: false,
        );
        
        expect(person.quantityDisplayText, '3');
      });
    });

    group('canBeDeleted', () {
      test('returns true when there are multiple people', () {
        const person = Person(id: 'test-id');
        
        expect(person.canBeDeleted(3), true);
        expect(person.canBeDeleted(2), true);
      });

      test('returns false when there is only one person', () {
        const person = Person(id: 'test-id');
        
        expect(person.canBeDeleted(1), false);
      });
    });

    group('canAddQuantity', () {
      test('returns true when person is assigned', () {
        const person = Person(id: 'test-id', isAssigned: true);
        
        expect(person.canAddQuantity, true);
      });

      test('returns false when person is not assigned', () {
        const person = Person(id: 'test-id', isAssigned: false);
        
        expect(person.canAddQuantity, false);
      });
    });

    group('canSubtractQuantity', () {
      test('returns true when assigned and has quantity > 0 and not equal split', () {
        const person = Person(
          id: 'test-id',
          isAssigned: true,
          quantity: 2,
          isEqualSplit: false,
        );
        
        expect(person.canSubtractQuantity, true);
      });

      test('returns false when not assigned', () {
        const person = Person(
          id: 'test-id',
          isAssigned: false,
          quantity: 2,
          isEqualSplit: false,
        );
        
        expect(person.canSubtractQuantity, false);
      });

      test('returns false when quantity is 0', () {
        const person = Person(
          id: 'test-id',
          isAssigned: true,
          quantity: 0,
          isEqualSplit: false,
        );
        
        expect(person.canSubtractQuantity, false);
      });

      test('returns false when using equal split', () {
        const person = Person(
          id: 'test-id',
          isAssigned: true,
          quantity: 2,
          isEqualSplit: true,
        );
        
        expect(person.canSubtractQuantity, false);
      });
    });
  });
}
