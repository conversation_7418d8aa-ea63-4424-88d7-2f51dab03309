import 'package:bill_splitter_2/bill/people/models/person.dart';
import 'package:bill_splitter_2/bill/people/models/item_assignment.dart';
import 'package:bill_splitter_2/bill/people/repositories/person_repository_implementation.dart';
import 'package:bill_splitter_2/local_storage/models/local_storage_key.dart';
import 'package:bill_splitter_2/local_storage/services/local_storage_service.dart';
import 'package:bill_splitter_2/unique_identifier/services/unique_identifier_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockLocalStorageService extends Mock implements LocalStorageService {
  @override
  Future<void> setValue<T>(LocalStorageKey key, T value) {
    return super.noSuchMethod(
      Invocation.method(#setValue, [key, value]),
    ) ?? Future<void>.value();
  }
}

class MockUniqueIdentifierService extends Mock implements UniqueIdentifierService {}

void main() {
  late MockLocalStorageService mockLocalStorageService;
  late MockUniqueIdentifierService mockUniqueIdentifierService;
  late PersonRepositoryImplementation personRepository;

  setUpAll(() {
    registerFallbackValue(LocalStorageKey.peopleList);
    registerFallbackValue(LocalStorageKey.itemAssignments);
  });

  setUp(() {
    mockLocalStorageService = MockLocalStorageService();
    mockUniqueIdentifierService = MockUniqueIdentifierService();

    // Set up default mock behavior for setValue
    when(() => mockLocalStorageService.setValue(any(), any()))
        .thenAnswer((_) => Future<void>.value());

    personRepository = PersonRepositoryImplementation(
      localStorageService: mockLocalStorageService,
      uniqueIdentifierService: mockUniqueIdentifierService,
    );
  });

  group('PersonRepositoryImplementation', () {
    group('getPeopleList', () {
      test('returns empty list when no people exist in storage', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.peopleList,
            )).thenAnswer((_) async => null);

        // Act
        final result = await personRepository.getPeopleList();

        // Assert
        expect(result, []);
      });

      test('returns list of people from storage', () async {
        // Arrange
        final peopleJson = [
          {
            'id': 'person1',
            'number': 1,
            'name': 'Alice',
            'quantity': 2,
            'amount': 10.0,
            'isAssigned': true,
            'isEqualSplit': false,
          },
          {
            'id': 'person2',
            'number': 2,
            'name': 'Bob',
            'quantity': 1,
            'amount': 15.0,
            'isAssigned': false,
            'isEqualSplit': true,
          },
        ];

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.peopleList,
            )).thenAnswer((_) async => peopleJson);

        // Act
        final result = await personRepository.getPeopleList();

        // Assert
        expect(result.length, 2);
        expect(result[0].id, 'person1');
        expect(result[0].name, 'Alice');
        expect(result[1].id, 'person2');
        expect(result[1].name, 'Bob');
      });
    });

    group('generatePerson', () {
      test('generates person with unique ID and specified number', () {
        // Arrange
        when(() => mockUniqueIdentifierService.generate())
            .thenReturn('unique-id');

        // Act
        final result = personRepository.generatePerson(3);

        // Assert
        expect(result.id, 'unique-id');
        expect(result.number, 3);
        expect(result.name, '');
        expect(result.quantity, 0);
        expect(result.amount, 0.0);
        expect(result.isAssigned, false);
        expect(result.isEqualSplit, false);
      });
    });

    group('createPerson', () {
      test('creates person and saves to storage', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.peopleList,
            )).thenAnswer((_) async => []);

        when(() => mockUniqueIdentifierService.generate())
            .thenReturn('new-person-id');

        // Act
        final result = await personRepository.createPerson();

        // Assert
        expect(result.length, 1);
        expect(result[0].id, 'new-person-id');
        expect(result[0].number, 1);

        verify(() => mockLocalStorageService.setValue(
              LocalStorageKey.peopleList,
              any(),
            )).called(1);
      });

      test('creates person with existing people and assigns next number', () async {
        // Arrange
        final existingPeople = [
          const Person(id: 'person1', number: 1),
          const Person(id: 'person2', number: 2),
        ];

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.peopleList,
            )).thenAnswer((_) async => existingPeople.map((p) => p.toJson()).toList());

        when(() => mockUniqueIdentifierService.generate())
            .thenReturn('new-person-id');

        // Act
        final result = await personRepository.createPerson();

        // Assert
        expect(result.length, 3);
        expect(result[2].id, 'new-person-id');
        expect(result[2].number, 3);
      });

      test('creates specific person when provided', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.peopleList,
            )).thenAnswer((_) async => []);

        const customPerson = Person(
          id: 'custom-id',
          number: 5,
          name: 'Custom',
        );

        // Act
        final result = await personRepository.createPerson(customPerson);

        // Assert
        expect(result.length, 1);
        expect(result[0].id, 'custom-id');
        expect(result[0].number, 5);
        expect(result[0].name, 'Custom');
      });
    });

    group('updatePerson', () {
      test('updates existing person in storage', () async {
        // Arrange
        final existingPeople = [
          const Person(id: 'person1', number: 1, name: 'Alice'),
          const Person(id: 'person2', number: 2, name: 'Bob'),
        ];

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.peopleList,
            )).thenAnswer((_) async => existingPeople.map((p) => p.toJson()).toList());

        const updatedPerson = Person(
          id: 'person1',
          number: 1,
          name: 'Alice Updated',
          quantity: 2,
        );

        // Act
        final result = await personRepository.updatePerson(updatedPerson);

        // Assert
        expect(result.length, 2);
        expect(result[0].name, 'Alice Updated');
        expect(result[0].quantity, 2);
        expect(result[1].name, 'Bob'); // unchanged

        verify(() => mockLocalStorageService.setValue(
              LocalStorageKey.peopleList,
              any(),
            )).called(1);
      });

      test('throws error when person not found', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.peopleList,
            )).thenAnswer((_) async => []);

        const nonExistentPerson = Person(id: 'non-existent');

        // Act & Assert
        expect(
          () => personRepository.updatePerson(nonExistentPerson),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('deletePerson', () {
      test('deletes person and renumbers remaining people', () async {
        // Arrange
        final existingPeople = [
          const Person(id: 'person1', number: 1, name: 'Alice'),
          const Person(id: 'person2', number: 2, name: 'Bob'),
          const Person(id: 'person3', number: 3, name: 'Charlie'),
        ];

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.peopleList,
            )).thenAnswer((_) async => existingPeople.map((p) => p.toJson()).toList());

        // Act
        final result = await personRepository.deletePerson('person2');

        // Assert
        expect(result.length, 2);
        expect(result[0].id, 'person1');
        expect(result[0].number, 1);
        expect(result[1].id, 'person3');
        expect(result[1].number, 2); // renumbered

        verify(() => mockLocalStorageService.setValue(
              LocalStorageKey.peopleList,
              any(),
            )).called(1);
      });

      test('throws error when trying to delete only person', () async {
        // Arrange
        final singlePerson = [
          const Person(id: 'person1', number: 1, name: 'Alice'),
        ];

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.peopleList,
            )).thenAnswer((_) async => singlePerson.map((p) => p.toJson()).toList());

        // Act & Assert
        expect(
          () => personRepository.deletePerson('person1'),
          throwsA(isA<StateError>()),
        );
      });

      test('throws error when person not found', () async {
        // Arrange
        final existingPeople = [
          const Person(id: 'person1', number: 1, name: 'Alice'),
        ];

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.peopleList,
            )).thenAnswer((_) async => existingPeople.map((p) => p.toJson()).toList());

        // Act & Assert
        expect(
          () => personRepository.deletePerson('non-existent'),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('getItemAssignments', () {
      test('returns empty list when no assignments exist in storage', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.itemAssignments,
            )).thenAnswer((_) async => null);

        // Act
        final result = await personRepository.getItemAssignments();

        // Assert
        expect(result, []);
      });

      test('returns list of item assignments from storage', () async {
        // Arrange
        final assignmentsJson = [
          {
            'itemId': 'item1',
            'assignedPeople': [],
            'isExpanded': false,
          },
        ];

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.itemAssignments,
            )).thenAnswer((_) async => assignmentsJson);

        // Act
        final result = await personRepository.getItemAssignments();

        // Assert
        expect(result.length, 1);
        expect(result[0].itemId, 'item1');
        expect(result[0].isExpanded, false);
      });
    });

    group('updateItemAssignment', () {
      test('adds new assignment when it does not exist', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.itemAssignments,
            )).thenAnswer((_) async => []);

        const newAssignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [],
          isExpanded: true,
        );

        // Act
        final result = await personRepository.updateItemAssignment(newAssignment);

        // Assert
        expect(result.length, 1);
        expect(result[0].itemId, 'item1');
        expect(result[0].isExpanded, true);

        verify(() => mockLocalStorageService.setValue(
              LocalStorageKey.itemAssignments,
              any(),
            )).called(1);
      });

      test('updates existing assignment', () async {
        // Arrange
        final existingAssignments = [
          const ItemAssignment(itemId: 'item1', assignedPeople: [], isExpanded: false),
        ];

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.itemAssignments,
            )).thenAnswer((_) async => existingAssignments.map((a) => a.toJson()).toList());

        const updatedAssignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [],
          isExpanded: true,
        );

        // Act
        final result = await personRepository.updateItemAssignment(updatedAssignment);

        // Assert
        expect(result.length, 1);
        expect(result[0].itemId, 'item1');
        expect(result[0].isExpanded, true);
      });
    });

    group('assignPersonToItem', () {
      test('creates new assignment when none exists', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.itemAssignments,
            )).thenAnswer((_) async => []);

        final existingPeople = [
          const Person(id: 'person1', number: 1, name: 'Alice'),
        ];

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.peopleList,
            )).thenAnswer((_) async => existingPeople.map((p) => p.toJson()).toList());

        // Act
        final result = await personRepository.assignPersonToItem('item1', 'person1');

        // Assert
        expect(result.length, 1);
        expect(result[0].itemId, 'item1');
        expect(result[0].assignedPeople.length, 1);
        expect(result[0].assignedPeople[0].isAssigned, true);
      });

      test('updates existing assignment', () async {
        // Arrange
        const person = Person(id: 'person1', number: 1, name: 'Alice', isAssigned: false);
        final existingAssignments = [
          const ItemAssignment(
            itemId: 'item1',
            assignedPeople: [person],
          ),
        ];

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.itemAssignments,
            )).thenAnswer((_) async => existingAssignments.map((a) => a.toJson()).toList());

        // Act
        final result = await personRepository.assignPersonToItem('item1', 'person1');

        // Assert
        expect(result.length, 1);
        expect(result[0].assignedPeople[0].isAssigned, true);
      });
    });

    group('unassignPersonFromItem', () {
      test('unassigns person from existing assignment', () async {
        // Arrange
        const person = Person(id: 'person1', number: 1, name: 'Alice', isAssigned: true, amount: 10.0);
        final existingAssignments = [
          const ItemAssignment(
            itemId: 'item1',
            assignedPeople: [person],
          ),
        ];

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.itemAssignments,
            )).thenAnswer((_) async => existingAssignments.map((a) => a.toJson()).toList());

        // Act
        final result = await personRepository.unassignPersonFromItem('item1', 'person1');

        // Assert
        expect(result.length, 1);
        expect(result[0].assignedPeople[0].isAssigned, false);
        expect(result[0].assignedPeople[0].quantity, 0);
        expect(result[0].assignedPeople[0].amount, 0.0);
      });

      test('does nothing when assignment does not exist', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.itemAssignments,
            )).thenAnswer((_) async => []);

        // Act
        final result = await personRepository.unassignPersonFromItem('item1', 'person1');

        // Assert
        expect(result, []);
      });
    });

    group('updatePersonQuantityForItem', () {
      test('updates person quantity and equal split status', () async {
        // Arrange
        const person = Person(id: 'person1', number: 1, name: 'Alice', quantity: 1, isEqualSplit: false);
        final existingAssignments = [
          const ItemAssignment(
            itemId: 'item1',
            assignedPeople: [person],
          ),
        ];

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.itemAssignments,
            )).thenAnswer((_) async => existingAssignments.map((a) => a.toJson()).toList());

        // Act
        final result = await personRepository.updatePersonQuantityForItem(
          'item1',
          'person1',
          3,
          true,
        );

        // Assert
        expect(result.length, 1);
        expect(result[0].assignedPeople[0].quantity, 3);
        expect(result[0].assignedPeople[0].isEqualSplit, true);
      });

      test('does nothing when assignment does not exist', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.itemAssignments,
            )).thenAnswer((_) async => []);

        // Act
        final result = await personRepository.updatePersonQuantityForItem(
          'item1',
          'person1',
          3,
          true,
        );

        // Assert
        expect(result, []);
      });
    });
  });
}
