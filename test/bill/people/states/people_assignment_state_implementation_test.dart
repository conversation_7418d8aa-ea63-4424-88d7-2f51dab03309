import 'package:bill_splitter_2/bill/items/models/item.dart';
import 'package:bill_splitter_2/bill/items/models/tip.dart';
import 'package:bill_splitter_2/bill/items/repositories/item_repository.dart';
import 'package:bill_splitter_2/bill/items/repositories/tip_repository.dart';
import 'package:bill_splitter_2/bill/people/models/person.dart';
import 'package:bill_splitter_2/bill/people/models/item_assignment.dart';
import 'package:bill_splitter_2/bill/people/repositories/person_repository.dart';
import 'package:bill_splitter_2/bill/people/states/people_assignment_state_implementation.dart';
import 'package:bill_splitter_2/localization/repositories/locale_repository.dart';
import 'package:bill_splitter_2/localization/repositories/strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockPersonRepository extends Mock implements PersonRepository {}
class MockItemRepository extends Mock implements ItemRepository {}
class MockTipRepository extends Mock implements TipRepository {}
class MockLocaleRepository extends Mock implements LocaleRepository {}
class MockStrings extends Mock implements Strings {}

class MockBuildContext extends Mock implements BuildContext {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late MockPersonRepository mockPersonRepository;
  late MockItemRepository mockItemRepository;
  late MockTipRepository mockTipRepository;
  late MockLocaleRepository mockLocaleRepository;
  late MockStrings mockStrings;
  late PeopleAssignmentStateImplementation state;

  const testItem1 = Item(id: 'item1', name: 'Pizza', price: 20.0, quantity: 2, total: 40.0);
  const testItem2 = Item(id: 'item2', name: 'Drinks', price: 5.0, quantity: 4, total: 20.0);
  const testTip = Tip(percentage: 15, amount: 9.0);

  const testPerson1 = Person(id: 'person1', number: 1, name: 'Alice');
  const testPerson2 = Person(id: 'person2', number: 2, name: 'Bob');

  setUpAll(() {
    registerFallbackValue(const Person(id: 'fallback'));
    registerFallbackValue(const ItemAssignment(itemId: 'fallback', assignedPeople: []));
  });

  setUp(() {
    mockPersonRepository = MockPersonRepository();
    mockItemRepository = MockItemRepository();
    mockTipRepository = MockTipRepository();
    mockLocaleRepository = MockLocaleRepository();
    mockStrings = MockStrings();

    // Default mock setups
    when(() => mockPersonRepository.getPeopleList())
        .thenAnswer((_) async => [testPerson1, testPerson2]);
    when(() => mockItemRepository.getItemList())
        .thenAnswer((_) async => [testItem1, testItem2]);
    when(() => mockTipRepository.getTip())
        .thenAnswer((_) async => testTip);
    when(() => mockPersonRepository.getItemAssignments())
        .thenAnswer((_) async => []);
    when(() => mockLocaleRepository.strings).thenReturn(mockStrings);
    when(() => mockPersonRepository.createPerson(any()))
        .thenAnswer((_) async => [testPerson1, testPerson2]);
    when(() => mockPersonRepository.updatePerson(any()))
        .thenAnswer((_) async => [testPerson1, testPerson2]);
    when(() => mockPersonRepository.deletePerson(any()))
        .thenAnswer((_) async => [testPerson1]);
    when(() => mockPersonRepository.updateItemAssignment(any()))
        .thenAnswer((_) async => []);
    when(() => mockPersonRepository.generatePerson(any()))
        .thenReturn(const Person(id: 'new-person', number: 3));
    when(() => mockPersonRepository.addPersonToAllItemAssignments(any()))
        .thenAnswer((_) async => []);
    when(() => mockPersonRepository.removePersonFromAllItemAssignments(any()))
        .thenAnswer((_) async => []);

    state = PeopleAssignmentStateImplementation(
      personRepository: mockPersonRepository,
      itemRepository: mockItemRepository,
      tipRepository: mockTipRepository,
      localeRepository: mockLocaleRepository,
    );
  });

  group('PeopleAssignmentStateImplementation', () {
    group('initialization', () {
      test('loads data on initialization', () async {
        // Wait for initialization to complete
        await Future.delayed(Duration.zero);

        expect(state.itemList, [testItem1, testItem2]);
        expect(state.tip, testTip);
        expect(state.peopleList, [testPerson1, testPerson2]);
      });

      test('creates first person if none exist', () async {
        // Arrange
        when(() => mockPersonRepository.getPeopleList())
            .thenAnswer((_) async => []);

        // Act
        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Assert
        verify(() => mockPersonRepository.createPerson(any())).called(1);
      });

      test('expands first item by default', () async {
        // Wait for initialization to complete
        await Future.delayed(Duration.zero);

        expect(state.isItemExpanded('item1'), true);
        expect(state.isItemExpanded('item2'), false);
      });
    });

    group('person management', () {
      test('onPersonNameChanged updates person name', () async {
        // Wait for initialization
        await Future.delayed(Duration.zero);

        // Act
        state.onPersonNameChanged('person1', 'Alice Updated');

        // Assert
        verify(() => mockPersonRepository.updatePerson(any())).called(1);
      });

      test('addPerson creates new person', () async {
        // Wait for initialization
        await Future.delayed(Duration.zero);

        // Act
        state.addPerson();

        // Assert
        verify(() => mockPersonRepository.createPerson(any())).called(1);
      });

      test('removePerson deletes person when multiple exist', () async {
        // Wait for initialization
        await Future.delayed(Duration.zero);

        // Act
        state.removePerson('person1');

        // Assert
        verify(() => mockPersonRepository.deletePerson('person1')).called(1);
      });

      test('removePerson does nothing when only one person exists', () async {
        // Arrange
        when(() => mockPersonRepository.getPeopleList())
            .thenAnswer((_) async => [testPerson1]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        state.removePerson('person1');

        // Assert
        verifyNever(() => mockPersonRepository.deletePerson(any()));
      });
    });

    group('item expansion', () {
      test('toggleItemExpansion expands collapsed item', () async {
        // Wait for initialization
        await Future.delayed(Duration.zero);

        // Act
        state.toggleItemExpansion('item2');

        // Assert
        expect(state.isItemExpanded('item1'), false);
        expect(state.isItemExpanded('item2'), true);
      });

      test('toggleItemExpansion collapses expanded item', () async {
        // Wait for initialization
        await Future.delayed(Duration.zero);

        // Act
        state.toggleItemExpansion('item1');

        // Assert
        expect(state.isItemExpanded('item1'), false);
        expect(state.isItemExpanded('item2'), false);
      });
    });

    group('person assignment', () {
      test('togglePersonAssignment assigns unassigned person', () async {
        // Arrange - Create assignment with all people but none assigned
        const unassignedPerson1 = Person(
          id: 'person1',
          number: 1,
          name: 'Alice',
          isAssigned: false,
        );
        const unassignedPerson2 = Person(
          id: 'person2',
          number: 2,
          name: 'Bob',
          isAssigned: false,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [unassignedPerson1, unassignedPerson2],
          isExpanded: true,
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        state.togglePersonAssignment('item1', 'person1');

        // Assert
        verify(() => mockPersonRepository.updateItemAssignment(any())).called(greaterThan(0));
      });

      test('togglePersonAssignment unassigns assigned person', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          number: 1,
          name: 'Alice',
          isAssigned: true,
          amount: 10.0,
        );
        const unassignedPerson = Person(
          id: 'person2',
          number: 2,
          name: 'Bob',
          isAssigned: false,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson, unassignedPerson],
          isExpanded: true,
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        state.togglePersonAssignment('item1', 'person1');

        // Assert
        verify(() => mockPersonRepository.updateItemAssignment(any())).called(greaterThan(0));
      });
    });

    group('quantity management', () {
      test('addPersonQuantity converts equal split to quantity 1', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
          isEqualSplit: true,
        );
        const unassignedPerson = Person(
          id: 'person2',
          number: 2,
          name: 'Bob',
          isAssigned: false,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson, unassignedPerson],
          isExpanded: true,
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        state.addPersonQuantity('item1', 'person1');

        // Assert
        verify(() => mockPersonRepository.updateItemAssignment(any())).called(greaterThan(0));
      });

      test('addPersonQuantity increases quantity by 1', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
          quantity: 2,
          isEqualSplit: false,
        );
        const unassignedPerson = Person(
          id: 'person2',
          number: 2,
          name: 'Bob',
          isAssigned: false,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson, unassignedPerson],
          isExpanded: true,
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        state.addPersonQuantity('item1', 'person1');

        // Assert
        verify(() => mockPersonRepository.updateItemAssignment(any())).called(greaterThan(0));
      });

      test('subtractPersonQuantity converts quantity 1 to equal split', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
          quantity: 1,
          isEqualSplit: false,
        );
        const unassignedPerson = Person(
          id: 'person2',
          number: 2,
          name: 'Bob',
          isAssigned: false,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson, unassignedPerson],
          isExpanded: true,
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        state.subtractPersonQuantity('item1', 'person1');

        // Assert
        verify(() => mockPersonRepository.updateItemAssignment(any())).called(greaterThan(0));
      });

      test('subtractPersonQuantity decreases quantity by 1', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
          quantity: 3,
          isEqualSplit: false,
        );
        const unassignedPerson = Person(
          id: 'person2',
          number: 2,
          name: 'Bob',
          isAssigned: false,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson, unassignedPerson],
          isExpanded: true,
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        state.subtractPersonQuantity('item1', 'person1');

        // Assert
        verify(() => mockPersonRepository.updateItemAssignment(any())).called(greaterThan(0));
      });
    });

    group('display methods', () {
      test('getItemAssignmentSummary returns empty string for no assignments', () async {
        // Wait for initialization
        await Future.delayed(Duration.zero);

        // Act
        final result = state.getItemAssignmentSummary('item1');

        // Assert
        expect(result, '');
      });

      test('getItemUnassignedQuantityText returns text when quantity unassigned', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
          quantity: 1,
          isEqualSplit: false,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson],
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        final result = state.getItemUnassignedQuantityText('item1');

        // Assert
        expect(result, 'Unassigned quantity: 1'); // item1 has quantity 2, assigned 1
      });

      test('isItemFullyAssigned returns true when all quantity assigned', () async {
        // Arrange
        const assignedPerson1 = Person(
          id: 'person1',
          isAssigned: true,
          quantity: 1,
          isEqualSplit: false,
        );

        const assignedPerson2 = Person(
          id: 'person2',
          isAssigned: true,
          quantity: 1,
          isEqualSplit: false,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson1, assignedPerson2],
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        final result = state.isItemFullyAssigned('item1');

        // Assert
        expect(result, true); // item1 has quantity 2, both people assigned 1 each
      });

      test('isItemFullyAssigned returns true for equal split assignments', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
          isEqualSplit: true,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson],
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        final result = state.isItemFullyAssigned('item1');

        // Assert
        expect(result, true); // Equal split is always considered fully assigned
      });
    });

    group('person state methods', () {
      test('isPersonAssignedToItem returns correct assignment status', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
        );

        const unassignedPerson = Person(
          id: 'person2',
          isAssigned: false,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson, unassignedPerson],
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act & Assert
        expect(state.isPersonAssignedToItem('item1', 'person1'), true);
        expect(state.isPersonAssignedToItem('item1', 'person2'), false);
      });

      test('canPersonAddQuantity returns true when assigned and multiple people', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson],
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        final result = state.canPersonAddQuantity('item1', 'person1');

        // Assert
        expect(result, true); // Person is assigned and there are 2 people total
      });

      test('canPersonSubtractQuantity returns true when conditions met', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
          quantity: 2,
          isEqualSplit: false,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson],
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        final result = state.canPersonSubtractQuantity('item1', 'person1');

        // Assert
        expect(result, true); // Person is assigned, not equal split, quantity > 0
      });
    });

    group('total calculations', () {
      test('totalAssignedAmount calculates correctly', () async {
        // Arrange
        const assignedPerson1 = Person(
          id: 'person1',
          isAssigned: true,
          amount: 15.0,
        );

        const assignedPerson2 = Person(
          id: 'person2',
          isAssigned: true,
          amount: 10.0,
        );

        final assignment1 = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson1],
        );

        final assignment2 = ItemAssignment(
          itemId: 'item2',
          assignedPeople: [assignedPerson2],
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment1, assignment2]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        final result = state.totalAssignedAmount;

        // Assert
        expect(result, 25.0); // 15.0 + 10.0
      });

      test('totalTipAmount calculates correctly with tip', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
          amount: 20.0,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson],
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        final result = state.totalTipAmount;

        // Assert
        expect(result, 3.0); // 20.0 * 0.15 = 3.0
      });

      test('grandTotal calculates correctly', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
          amount: 20.0,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson],
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        final result = state.grandTotal;

        // Assert
        expect(result, 23.0); // 20.0 + 3.0
      });

      test('totalFirstLine shows amount with tip', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
          amount: 20.0,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson],
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        final result = state.totalFirstLine;

        // Assert
        expect(result, '\$20.00 + \$3.00');
      });

      test('totalFirstLine shows amount only when no tip', () async {
        // Arrange
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => null);

        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
          amount: 20.0,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson],
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        final result = state.totalFirstLine;

        // Assert
        expect(result, '\$20.00');
      });
    });

    group('navigation', () {
      test('canContinueToNextStep returns true when all items fully assigned', () async {
        // Arrange
        const assignedPerson1 = Person(
          id: 'person1',
          isAssigned: true,
          quantity: 2,
          isEqualSplit: false,
        );

        const assignedPerson2 = Person(
          id: 'person2',
          isAssigned: true,
          quantity: 4,
          isEqualSplit: false,
        );

        final assignment1 = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson1], // item1 has quantity 2
        );

        final assignment2 = ItemAssignment(
          itemId: 'item2',
          assignedPeople: [assignedPerson2], // item2 has quantity 4
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment1, assignment2]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        final result = state.canContinueToNextStep;

        // Assert
        expect(result, true);
      });

      test('canContinueToNextStep returns false when items not fully assigned', () async {
        // Arrange
        const assignedPerson = Person(
          id: 'person1',
          isAssigned: true,
          quantity: 1,
          isEqualSplit: false,
        );

        final assignment = ItemAssignment(
          itemId: 'item1',
          assignedPeople: [assignedPerson], // item1 has quantity 2, only 1 assigned
        );

        when(() => mockPersonRepository.getItemAssignments())
            .thenAnswer((_) async => [assignment]);

        state = PeopleAssignmentStateImplementation(
          personRepository: mockPersonRepository,
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
        );

        await Future.delayed(Duration.zero);

        // Act
        final result = state.canContinueToNextStep;

        // Assert
        expect(result, false);
      });

      test('returnToPreviousStep collapses all items', () async {
        // Wait for initialization
        await Future.delayed(Duration.zero);

        // First item should be expanded by default, expand second item too
        expect(state.isItemExpanded('item1'), true);
        state.toggleItemExpansion('item2');
        expect(state.isItemExpanded('item2'), true);

        // Act - Test only the collapsing behavior by directly calling the collapse logic
        // We can't test the full method due to GoRouter dependency in test environment
        for (int i = 0; i < state.itemAssignments.length; i++) {
          final assignment = state.itemAssignments[i];
          if (assignment.isExpanded) {
            state.toggleItemExpansion(assignment.itemId);
          }
        }

        // Assert
        expect(state.isItemExpanded('item1'), false);
        expect(state.isItemExpanded('item2'), false);
      });
    });
  });
}
