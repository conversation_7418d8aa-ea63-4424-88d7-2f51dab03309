import 'package:bill_splitter_2/bill/people/views/assignment_total.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AssignmentTotal', () {
    testWidgets('displays first and second line correctly', (tester) async {
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AssignmentTotal(
              firstLine: '\$50.00 + \$7.50',
              secondLine: '\$57.50',
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('\$50.00 + \$7.50'), findsOneWidget);
      expect(find.text('\$57.50'), findsOneWidget);
    });

    testWidgets('displays total without tip correctly', (tester) async {
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AssignmentTotal(
              firstLine: '\$50.00',
              secondLine: '\$50.00',
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('\$50.00'), findsNWidgets(2)); // Both lines show same amount
    });

    testWidgets('applies correct text styles', (tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            textTheme: const TextTheme(
              bodyMedium: TextStyle(fontSize: 14),
              titleLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
            ),
          ),
          home: const Scaffold(
            body: AssignmentTotal(
              firstLine: 'First Line',
              secondLine: 'Second Line',
            ),
          ),
        ),
      );

      // Assert
      final firstLineText = tester.widget<Text>(find.text('First Line'));
      final secondLineText = tester.widget<Text>(find.text('Second Line'));

      expect(firstLineText.style?.fontSize, 14);
      expect(secondLineText.style?.fontSize, 22);
      expect(secondLineText.style?.fontWeight, FontWeight.bold);
    });
  });
}
