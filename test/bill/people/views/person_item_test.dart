import 'package:bill_splitter_2/bill/people/models/person.dart';
import 'package:bill_splitter_2/bill/people/views/person_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PersonItem', () {
    const testPerson = Person(
      id: 'person1',
      number: 1,
      name: 'Alice',
      quantity: 2,
      amount: 15.0,
      isAssigned: true,
      isEqualSplit: false,
    );

    testWidgets('displays person information correctly', (tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PersonItem(
              person: testPerson,
              isAssigned: true,
              canAddQuantity: true,
              canSubtractQuantity: true,
              isCheckboxEnabled: true,
              quantity: 2,
              isEqualSplit: false,
              amount: 15.0,
              canBeDeleted: true,
              onNameChanged: (_) {},
              onToggleAssignment: () {},
              onAddQuantity: () {},
              onSubtractQuantity: () {},
              onDelete: () {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('1'), findsOneWidget); // Person number
      expect(find.text('Alice'), findsOneWidget); // Person name in text field
      expect(find.text('2'), findsOneWidget); // Quantity display
      expect(find.text('\$15.00'), findsOneWidget); // Amount display
      expect(find.byType(Checkbox), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);
      expect(find.byIcon(Icons.remove), findsOneWidget);
      expect(find.byIcon(Icons.delete), findsOneWidget);
    });

    testWidgets('shows equal split text when isEqualSplit is true', (tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PersonItem(
              person: testPerson,
              isAssigned: true,
              canAddQuantity: true,
              canSubtractQuantity: false,
              isCheckboxEnabled: true,
              quantity: 1,
              isEqualSplit: true,
              amount: 10.0,
              canBeDeleted: true,
              onNameChanged: (_) {},
              onToggleAssignment: () {},
              onAddQuantity: () {},
              onSubtractQuantity: () {},
              onDelete: () {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Equal split'), findsOneWidget);
    });

    testWidgets('hides delete button when canBeDeleted is false', (tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PersonItem(
              person: testPerson,
              isAssigned: true,
              canAddQuantity: true,
              canSubtractQuantity: true,
              isCheckboxEnabled: true,
              quantity: 2,
              isEqualSplit: false,
              amount: 15.0,
              canBeDeleted: false,
              onNameChanged: (_) {},
              onToggleAssignment: () {},
              onAddQuantity: () {},
              onSubtractQuantity: () {},
              onDelete: () {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.delete), findsNothing);
    });

    testWidgets('disables add button when canAddQuantity is false', (tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PersonItem(
              person: testPerson,
              isAssigned: false,
              canAddQuantity: false,
              canSubtractQuantity: false,
              isCheckboxEnabled: true,
              quantity: 0,
              isEqualSplit: false,
              amount: 0.0,
              canBeDeleted: true,
              onNameChanged: (_) {},
              onToggleAssignment: () {},
              onAddQuantity: () {},
              onSubtractQuantity: () {},
              onDelete: () {},
            ),
          ),
        ),
      );

      // Assert
      final addButton = tester.widget<IconButton>(
        find.byIcon(Icons.add),
      );
      expect(addButton.onPressed, isNull);
    });

    testWidgets('disables subtract button when canSubtractQuantity is false', (tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PersonItem(
              person: testPerson,
              isAssigned: true,
              canAddQuantity: true,
              canSubtractQuantity: false,
              isCheckboxEnabled: true,
              quantity: 0,
              isEqualSplit: true,
              amount: 10.0,
              canBeDeleted: true,
              onNameChanged: (_) {},
              onToggleAssignment: () {},
              onAddQuantity: () {},
              onSubtractQuantity: () {},
              onDelete: () {},
            ),
          ),
        ),
      );

      // Assert
      final subtractButton = tester.widget<IconButton>(
        find.byIcon(Icons.remove),
      );
      expect(subtractButton.onPressed, isNull);
    });

    testWidgets('disables checkbox when isCheckboxEnabled is false', (tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PersonItem(
              person: testPerson,
              isAssigned: false,
              canAddQuantity: false,
              canSubtractQuantity: false,
              isCheckboxEnabled: false,
              quantity: 0,
              isEqualSplit: false,
              amount: 0.0,
              canBeDeleted: true,
              onNameChanged: (_) {},
              onToggleAssignment: () {},
              onAddQuantity: () {},
              onSubtractQuantity: () {},
              onDelete: () {},
            ),
          ),
        ),
      );

      // Assert
      final checkbox = tester.widget<Checkbox>(find.byType(Checkbox));
      expect(checkbox.onChanged, isNull);
    });

    testWidgets('calls callbacks when interacted with', (tester) async {
      // Arrange
      bool nameChanged = false;
      bool assignmentToggled = false;
      bool quantityAdded = false;
      bool quantitySubtracted = false;
      bool deleted = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PersonItem(
              person: testPerson,
              isAssigned: true,
              canAddQuantity: true,
              canSubtractQuantity: true,
              isCheckboxEnabled: true,
              quantity: 2,
              isEqualSplit: false,
              amount: 15.0,
              canBeDeleted: true,
              onNameChanged: (_) => nameChanged = true,
              onToggleAssignment: () => assignmentToggled = true,
              onAddQuantity: () => quantityAdded = true,
              onSubtractQuantity: () => quantitySubtracted = true,
              onDelete: () => deleted = true,
            ),
          ),
        ),
      );

      // Test name change
      await tester.enterText(find.byType(TextFormField), 'New Name');
      expect(nameChanged, isTrue);

      // Test checkbox toggle
      await tester.tap(find.byType(Checkbox));
      expect(assignmentToggled, isTrue);

      // Test add quantity
      await tester.tap(find.byIcon(Icons.add));
      expect(quantityAdded, isTrue);

      // Test subtract quantity
      await tester.tap(find.byIcon(Icons.remove));
      expect(quantitySubtracted, isTrue);

      // Test delete
      await tester.tap(find.byIcon(Icons.delete));
      expect(deleted, isTrue);
    });
  });
}
